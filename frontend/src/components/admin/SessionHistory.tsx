import { useState, useEffect } from 'react';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  Clock, 
  MapPin, 
  Activity,
  LogOut,
  AlertTriangle,
  RefreshCw,
  Trash2,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { userService } from '../../services/userService';
import { useModalContext } from '../../contexts/ModalContext';
import EndSessionModal from '../modals/EndSessionModal';
import SessionEndedModal from '../modals/SessionEndedModal';

interface Session {
  _id: string;
  session_id: string;
  user_id: string;
  ip_address: string;
  user_agent: string;
  login_time: string;
  logout_time?: string;
  is_active: boolean;
  remember_me: boolean;
  last_activity: string;
  location: string;
  device_info: {
    browser: string;
    os: string;
    device: string;
    raw: string;
  };
}

interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  first_login?: string;
  last_login?: string;
}

interface SessionHistoryProps {
  userId: string;
}

export default function SessionHistory({ userId }: SessionHistoryProps) {
  const { showAlert, showConfirm } = useModalContext();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [stats, setStats] = useState<SessionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeOnly, setActiveOnly] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showEndSessionModal, setShowEndSessionModal] = useState(false);
  const [showSessionEndedModal, setShowSessionEndedModal] = useState(false);
  const [sessionToEnd, setSessionToEnd] = useState<Session | null>(null);
  const [endedSessionInfo, setEndedSessionInfo] = useState<any>(null);
  const [endSessionLoading, setEndSessionLoading] = useState(false);

  useEffect(() => {
    fetchSessionData();
  }, [userId, activeOnly]);

  const fetchSessionData = async () => {
    try {
      setLoading(true);
      
      // Fetch sessions and stats in parallel
      const [sessionsResponse, statsResponse] = await Promise.all([
        userService.getUserSessions(userId, 50, activeOnly),
        userService.getUserSessionStats(userId)
      ]);
      
      setSessions(sessionsResponse.data);
      setStats(statsResponse.data);
    } catch (error) {
      showAlert({
        message: 'Failed to load session data',
        type: 'error',
        title: 'Error Loading Sessions'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSessionData();
    setRefreshing(false);
  };

  const handleEndSession = (session: Session) => {
    setSessionToEnd(session);
    setShowEndSessionModal(true);
  };

  const confirmEndSession = async () => {
    if (!sessionToEnd) return;

    try {
      setEndSessionLoading(true);
      await userService.endSpecificSession(sessionToEnd.session_id);

      // Store session info for success modal
      setEndedSessionInfo({
        device: sessionToEnd.device_info.device,
        browser: sessionToEnd.device_info.browser,
        os: sessionToEnd.device_info.os,
        ip_address: sessionToEnd.ip_address
      });

      setShowEndSessionModal(false);
      setSessionToEnd(null);
      setShowSessionEndedModal(true);

      // Refresh session data
      fetchSessionData();
    } catch (error) {
      showAlert({
        message: '❌ Échec de la terminaison de session. Veuillez réessayer.',
        type: 'error',
        title: 'Error Ending Session'
      });
    } finally {
      setEndSessionLoading(false);
    }
  };

  const closeEndSessionModal = () => {
    if (!endSessionLoading) {
      setShowEndSessionModal(false);
      setSessionToEnd(null);
    }
  };

  const handleEndAllSessions = async () => {
    const confirmed = await showConfirm(
      '🚨 Terminer toutes les sessions',
      `Êtes-vous sûr de vouloir terminer TOUTES les sessions actives de cet utilisateur ?\n\n⚠️ Cette action va :\n• Déconnecter l'utilisateur de tous ses appareils\n• Fermer toutes les sessions en cours\n• Forcer une nouvelle authentification\n\nCette action est recommandée en cas de compromission de compte.`,
      'Terminer toutes les sessions',
      'destructive'
    );

    if (confirmed) {
      try {
        await userService.endAllUserSessions(userId);
        showAlert({
          message: '✅ Toutes les sessions ont été terminées avec succès',
          type: 'success',
          title: 'Sessions Ended'
        });
        fetchSessionData();
      } catch (error) {
        showAlert({
          message: '❌ Échec de la terminaison des sessions. Veuillez réessayer.',
          type: 'error',
          title: 'Error Ending Sessions'
        });
      }
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <Smartphone size={16} className="text-blue-400" />;
      case 'tablet':
        return <Tablet size={16} className="text-green-400" />;
      default:
        return <Monitor size={16} className="text-gray-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatDuration = (loginTime: string, logoutTime?: string) => {
    const start = new Date(loginTime);
    const end = logoutTime ? new Date(logoutTime) : new Date();
    const duration = end.getTime() - start.getTime();
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-xs">Total Sessions</p>
                <p className="text-lg font-bold text-white">{stats.total_sessions}</p>
              </div>
              <Activity className="h-6 w-6 text-blue-400" />
            </div>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-xs">Active Sessions</p>
                <p className="text-lg font-bold text-green-400">{stats.active_sessions}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-green-400" />
            </div>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-xs">First Login</p>
                <p className="text-sm text-white">
                  {stats.first_login ? formatDate(stats.first_login) : 'Never'}
                </p>
              </div>
              <Clock className="h-6 w-6 text-purple-400" />
            </div>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-xs">Last Login</p>
                <p className="text-sm text-white">
                  {stats.last_login ? formatDate(stats.last_login) : 'Never'}
                </p>
              </div>
              <Activity className="h-6 w-6 text-yellow-400" />
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={activeOnly}
              onChange={(e) => setActiveOnly(e.target.checked)}
              className="rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
            />
            <span className="text-gray-300 text-sm">Show active sessions only</span>
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-3 py-2 rounded-lg flex items-center space-x-2 transition-colors text-sm"
          >
            <RefreshCw size={14} className={refreshing ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          
          {stats && stats.active_sessions > 0 && (
            <button
              onClick={handleEndAllSessions}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg flex items-center space-x-2 transition-colors text-sm"
            >
              <Trash2 size={14} />
              <span>End All Sessions</span>
            </button>
          )}
        </div>
      </div>

      {/* Sessions List */}
      <div className="space-y-3">
        {sessions.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            {activeOnly ? 'No active sessions found' : 'No sessions found'}
          </div>
        ) : (
          sessions.map((session) => (
            <div
              key={session._id}
              className={`bg-gray-700/30 border rounded-lg p-4 ${
                session.is_active ? 'border-green-500/30' : 'border-gray-600'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    {getDeviceIcon(session.device_info.device)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-white font-medium">
                        {session.device_info.browser} on {session.device_info.os}
                      </h4>
                      {session.is_active ? (
                        <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">Active</span>
                      ) : (
                        <span className="bg-gray-600 text-white px-2 py-1 rounded text-xs">Ended</span>
                      )}
                      {session.remember_me && (
                        <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">Remember Me</span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                      <div className="flex items-center">
                        <Globe size={12} className="mr-1" />
                        <span>{session.ip_address}</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin size={12} className="mr-1" />
                        <span>{session.location}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        <span>Login: {formatDate(session.login_time)}</span>
                      </div>
                      <div className="flex items-center">
                        {session.logout_time ? (
                          <>
                            <LogOut size={12} className="mr-1" />
                            <span>Logout: {formatDate(session.logout_time)}</span>
                          </>
                        ) : (
                          <>
                            <Activity size={12} className="mr-1" />
                            <span>Duration: {formatDuration(session.login_time)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                {session.is_active && (
                  <button
                    onClick={() => handleEndSession(session)}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs flex items-center space-x-1 transition-colors"
                  >
                    <XCircle size={12} />
                    <span>End</span>
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Modals */}
      {showEndSessionModal && sessionToEnd && (
        <EndSessionModal
          isOpen={showEndSessionModal}
          onClose={closeEndSessionModal}
          onConfirm={confirmEndSession}
          sessionInfo={{
            device: sessionToEnd.device_info.device,
            browser: sessionToEnd.device_info.browser,
            os: sessionToEnd.device_info.os,
            ip_address: sessionToEnd.ip_address,
            location: sessionToEnd.location,
            login_time: sessionToEnd.login_time
          }}
          loading={endSessionLoading}
        />
      )}

      {showSessionEndedModal && endedSessionInfo && (
        <SessionEndedModal
          isOpen={showSessionEndedModal}
          onClose={() => setShowSessionEndedModal(false)}
          sessionInfo={endedSessionInfo}
        />
      )}
    </div>
  );
}
