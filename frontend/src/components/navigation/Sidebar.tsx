
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  AlertCircle,
  BarChart,
  Settings,
  Shield,
  LogOut,
  Target,
  Bug,
  Globe,
  Database,
  Users
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../contexts/NavigationContext';

const Sidebar = () => {
  const location = useLocation();
  const { logout, user, role } = useAuth();
  const { setCurrentSection } = useNavigation();



  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="h-screen w-64 flex flex-col bg-gray-900/95 backdrop-blur-xl border-r border-gray-700/50 shadow-2xl">
      <div className="flex-1 flex flex-col">
        <div className="flex items-center justify-center h-16 w-full border-b border-gray-700/50">
          <Shield className="h-8 w-8 text-purple-400 drop-shadow-lg mr-3" />
          <span className="text-xl font-bold text-white">PICA</span>
        </div>
        <div className="flex-1 flex flex-col pt-6 space-y-2 px-4">
          <Link
            to="/app"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/app') || isActive('/pentesting')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Pentesting')}
          >
            <Target size={20} className="mr-3" />
            <span className="font-medium">Pentesting</span>
          </Link>
          <Link
            to="/dashboard"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/dashboard')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Dashboard')}
          >
            <LayoutDashboard size={20} className="mr-3" />
            <span className="font-medium">Dashboard</span>
          </Link>
          <Link
            to="/phishing"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/phishing')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Phishing Detection')}
          >
            <Globe size={20} className="mr-3" />
            <span className="font-medium">Phishing Detection</span>
          </Link>
          <Link
            to="/malware"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/malware')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Malware Detection')}
          >
            <Bug size={20} className="mr-3" />
            <span className="font-medium">Malware Detection</span>
          </Link>
          <Link
            to="/incidents"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/incidents')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Incidents')}
          >
            <AlertCircle size={20} className="mr-3" />
            <span className="font-medium">Incidents</span>
          </Link>
          <Link
            to="/analytics"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/analytics')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Analytics')}
          >
            <BarChart size={20} className="mr-3" />
            <span className="font-medium">Analytics</span>
          </Link>

          {/* Admin-only links */}
          {role === 'admin' && (
            <>
              <Link
                to="/user-management"
                className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
                  isActive('/user-management')
                    ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                    : 'text-gray-400 hover:text-white hover:bg-purple-600/80'
                }`}
                onClick={() => setCurrentSection('User Management')}
              >
                <Users size={20} className="mr-3" />
                <span className="font-medium">User Management</span>
              </Link>
            </>
          )}

          <Link
            to="/settings"
            className={`flex items-center w-full px-4 py-3 rounded-xl transition-all duration-200 ${
              isActive('/settings')
                ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
            }`}
            onClick={() => setCurrentSection('Settings')}
          >
            <Settings size={20} className="mr-3" />
            <span className="font-medium">Settings</span>
          </Link>
        </div>
      </div>
      <div className="p-4 border-t border-gray-700/50">
        <button
          onClick={handleLogout}
          className="flex items-center w-full px-4 py-3 rounded-xl text-gray-400 hover:text-white hover:bg-red-600/80 transition-all duration-200"
        >
          <LogOut size={20} className="mr-3" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar; 
