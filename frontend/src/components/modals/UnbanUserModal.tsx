import { useState } from 'react';
import { <PERSON>, User<PERSON><PERSON><PERSON>, CheckCircle, Shield, Unlock } from 'lucide-react';

interface UnbanUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  username: string;
  loading?: boolean;
}

export default function UnbanUserModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  username, 
  loading = false 
}: UnbanUserModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-green-500/30 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center">
              <UserCheck size={20} className="text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Débannir l'utilisateur</h2>
              <p className="text-gray-400 text-sm">Restaurer l'accès</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            disabled={loading}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center">
              <Unlock size={32} className="text-green-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              Débannir <span className="text-green-400">@{username}</span> ?
            </h3>
            <p className="text-gray-400 text-sm">
              Cette action va restaurer l'accès complet de l'utilisateur :
            </p>
          </div>

          {/* Restoration List */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Unlock size={16} className="text-green-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-green-300 font-medium text-sm">Accès à la connexion restauré</p>
                  <p className="text-green-400/80 text-xs">L'utilisateur pourra se connecter immédiatement</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Shield size={16} className="text-green-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-green-300 font-medium text-sm">Fonctionnalités débloquées</p>
                  <p className="text-green-400/80 text-xs">Accès complet à toutes les fonctionnalités</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle size={16} className="text-green-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-green-300 font-medium text-sm">Restrictions supprimées</p>
                  <p className="text-green-400/80 text-xs">Toutes les limitations de sécurité levées</p>
                </div>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
              <div>
                <p className="text-yellow-300 text-sm">
                  <span className="font-medium">Sécurité :</span> Assurez-vous que l'utilisateur respecte les conditions d'utilisation avant de le débannir.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <UserCheck size={16} />
            )}
            <span>{loading ? 'Débannissement...' : 'Débannir l\'utilisateur'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
