import { X, CheckCircle, User, Mail, Shield, Key, UserPlus } from 'lucide-react';

interface CreateUserSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    role: string;
  };
}

export default function CreateUserSuccessModal({ 
  isOpen, 
  onClose, 
  userData 
}: CreateUserSuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-green-500/30 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center">
              <CheckCircle size={20} className="text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Utilisateur créé</h2>
              <p className="text-gray-400 text-sm">Compte créé avec succès</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center">
              <UserPlus size={32} className="text-green-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              Le compte de <span className="text-green-400">{userData.first_name} {userData.last_name}</span> a été créé
            </h3>
            <p className="text-gray-400 text-sm">
              Voici les détails du nouveau compte utilisateur :
            </p>
          </div>

          {/* User Details */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <h4 className="text-green-300 font-medium text-sm mb-3 flex items-center">
              <User size={16} className="text-green-400" />
              <span className="ml-2">Informations du compte</span>
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Nom complet :</span>
                <span className="text-green-300">{userData.first_name} {userData.last_name}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Nom d'utilisateur :</span>
                <span className="text-green-300 font-mono">@{userData.username}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Email :</span>
                <span className="text-green-300">{userData.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Rôle :</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  userData.role === 'admin' ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white'
                }`}>
                  {userData.role === 'admin' ? 'Administrateur' : 'Utilisateur'}
                </span>
              </div>
            </div>
          </div>

          {/* Account Status */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <CheckCircle size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Compte activé et prêt à utiliser</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <Mail size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Email pré-vérifié (création admin)</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <Key size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Mot de passe configuré</p>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
              <div>
                <p className="text-blue-300 text-sm">
                  <span className="font-medium">Prochaines étapes :</span> L'utilisateur peut maintenant se connecter avec ses identifiants et commencer à utiliser la plateforme.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <CheckCircle size={16} />
            <span>Parfait</span>
          </button>
        </div>
      </div>
    </div>
  );
}
