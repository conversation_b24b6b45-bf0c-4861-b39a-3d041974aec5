import { X, CheckCircle, Trash2, Database, UserX } from 'lucide-react';

interface DeleteSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  username: string;
}

export default function DeleteSuccessModal({ 
  isOpen, 
  onClose, 
  username 
}: DeleteSuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-green-500/30 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center">
              <CheckCircle size={20} className="text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Utilisateur supprimé</h2>
              <p className="text-gray-400 text-sm">Suppression effectuée</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center">
              <Trash2 size={32} className="text-green-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              L'utilisateur <span className="text-red-400">@{username}</span> a été supprimé
            </h3>
            <p className="text-gray-400 text-sm">
              Les actions suivantes ont été effectuées :
            </p>
          </div>

          {/* Completed Actions */}
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <UserX size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Compte utilisateur supprimé</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <Database size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Données personnelles effacées</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-600/20 rounded-full flex items-center justify-center">
                  <CheckCircle size={12} className="text-green-400" />
                </div>
                <p className="text-green-300 text-sm">Sessions terminées</p>
              </div>
            </div>
          </div>

          {/* Final Notice */}
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
              <div>
                <p className="text-gray-300 text-sm">
                  <span className="font-medium">Terminé :</span> L'utilisateur et toutes ses données ont été définitivement supprimés du système.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <CheckCircle size={16} />
            <span>Compris</span>
          </button>
        </div>
      </div>
    </div>
  );
}
