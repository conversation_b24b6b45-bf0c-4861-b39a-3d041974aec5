import { useState } from 'react';
import { X, LogOut, AlertTriangle, Monitor, Smartphone, Tablet, Globe, Clock } from 'lucide-react';

interface EndSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  sessionInfo?: {
    device: string;
    browser: string;
    os: string;
    ip_address: string;
    location: string;
    login_time: string;
  };
  loading?: boolean;
}

export default function EndSessionModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  sessionInfo,
  loading = false 
}: EndSessionModalProps) {
  if (!isOpen) return null;

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType?.toLowerCase()) {
      case 'mobile':
        return <Smartphone size={16} className="text-orange-400" />;
      case 'tablet':
        return <Tablet size={16} className="text-orange-400" />;
      default:
        return <Monitor size={16} className="text-orange-400" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-orange-500/30 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-600/20 rounded-full flex items-center justify-center">
              <LogOut size={20} className="text-orange-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Terminer la session</h2>
              <p className="text-gray-400 text-sm">Déconnexion forcée</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            disabled={loading}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Warning Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-orange-600/20 rounded-full flex items-center justify-center">
              <AlertTriangle size={32} className="text-orange-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              Êtes-vous sûr de vouloir terminer cette session ?
            </h3>
            <p className="text-gray-400 text-sm">
              L'utilisateur sera immédiatement déconnecté de cet appareil.
            </p>
          </div>

          {/* Session Info */}
          {sessionInfo && (
            <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4">
              <h4 className="text-orange-300 font-medium text-sm mb-3 flex items-center">
                {getDeviceIcon(sessionInfo.device)}
                <span className="ml-2">Informations de la session</span>
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Appareil :</span>
                  <span className="text-orange-300">{sessionInfo.browser} sur {sessionInfo.os}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Adresse IP :</span>
                  <span className="text-orange-300 font-mono">{sessionInfo.ip_address}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Localisation :</span>
                  <span className="text-orange-300">{sessionInfo.location}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Connecté depuis :</span>
                  <span className="text-orange-300">{formatDate(sessionInfo.login_time)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Consequences */}
          <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <LogOut size={16} className="text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-300 font-medium text-sm">Déconnexion immédiate</p>
                  <p className="text-yellow-400/80 text-xs">L'utilisateur sera déconnecté instantanément</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <AlertTriangle size={16} className="text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-300 font-medium text-sm">Perte de travail non sauvegardé</p>
                  <p className="text-yellow-400/80 text-xs">Toute donnée non sauvegardée sera perdue</p>
                </div>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
              <div>
                <p className="text-blue-300 text-sm">
                  <span className="font-medium">Sécurité :</span> Cette action est recommandée en cas d'activité suspecte ou de session compromise.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="bg-orange-600 hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <LogOut size={16} />
            )}
            <span>{loading ? 'Terminaison...' : 'Terminer la session'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
