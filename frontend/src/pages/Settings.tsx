import { useState, useEffect } from 'react';
import {
  User,
  Bell,
  Shield,
  Users,
  Zap,
  Key,
  Camera,
  Save,
  Mail,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { getToken } from '../utils/auth';

interface NotificationSettings {
  email_enabled: boolean;
  email_address: string;
  telegram_enabled: boolean;
  telegram_chat_id: string;
}

export default function Settings() {
  const { user } = useAuth();
  const { isAdmin } = useRole();
  const [activeTab, setActiveTab] = useState('profile');

  // Debug: log user data
  useEffect(() => {
    console.log('Current user from useAuth:', user);
    console.log('Is admin:', isAdmin);
  }, [user, isAdmin]);
  const [timezone, setTimezone] = useState('UTC');
  const [dateFormat, setDateFormat] = useState('MMDD, YYYY (Jun 15, 2023)');

  // Current user data (read-only display)
  const [currentFirstName, setCurrentFirstName] = useState('');
  const [currentLastName, setCurrentLastName] = useState('');
  const [currentUsername, setCurrentUsername] = useState('');
  const [currentEmail, setCurrentEmail] = useState('');
  const [currentRole, setCurrentRole] = useState('');
  const [currentAvatar, setCurrentAvatar] = useState('');

  // Form states (for new values)
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('');
  const [avatar, setAvatar] = useState('');

  // Loading and error states
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});
  const [dataLoading, setDataLoading] = useState(true);

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_enabled: false,
    email_address: '',
    telegram_enabled: false,
    telegram_chat_id: ''
  });
  const [notificationLoading, setNotificationLoading] = useState(false);
  const [notificationSuccess, setNotificationSuccess] = useState<string | null>(null);
  const [notificationError, setNotificationError] = useState<string | null>(null);
 

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'team', label: 'Team', icon: Users },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'api', label: 'API Access', icon: Key },
  ];

  useEffect(() => {
    if (isAdmin && activeTab === 'notifications') {
      loadNotificationSettings();
    }
  }, [isAdmin, activeTab]);

  useEffect(() => {
    loadUserProfile();
  }, [user]);

  const loadUserProfile = async () => {
    setDataLoading(true);
    try {
      const token = getToken();
      if (!token) {
        console.error('No authentication token found');
        setDataLoading(false);
        return;
      }

      console.log('Loading user data from API...');
      const response = await fetch('http://localhost:5000/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('User data from API:', userData);

        // Set current user data (for display)
        setCurrentFirstName(userData.first_name || '');
        setCurrentLastName(userData.last_name || '');
        setCurrentUsername(userData.username || '');
        setCurrentEmail(userData.email || '');
        setCurrentRole(userData.role || '');
        setCurrentAvatar(userData.avatar || '');

        // Keep form fields empty for new values
        setFirstName('');
        setLastName('');
        setUsername('');
        setEmail('');
        setRole(userData.role || '');
        setAvatar(userData.avatar || '');
        setTimezone(userData.timezone || 'UTC');
        setDateFormat(userData.date_format || 'MMDD, YYYY (Jun 15, 2023)');
      } else {
        console.error('Failed to load user profile from API:', response.status);

        // Fallback to context data if API fails
        if (user) {
          console.log('Falling back to context data:', user);
          setCurrentFirstName(user.first_name || user.firstName || '');
          setCurrentLastName(user.last_name || user.lastName || '');
          setCurrentUsername(user.username || '');
          setCurrentEmail(user.email || '');
          setCurrentRole(user.role || '');
          setCurrentAvatar(user.avatar || '');
          setTimezone(user.timezone || 'UTC');
          setDateFormat(user.date_format || 'MMDD, YYYY (Jun 15, 2023)');
        }
      }
    } catch (err) {
      console.error('Failed to load user profile:', err);

      // Fallback to context data if API call fails
      if (user) {
        console.log('Falling back to context data due to error:', user);
        setCurrentFirstName(user.first_name || user.firstName || '');
        setCurrentLastName(user.last_name || user.lastName || '');
        setCurrentUsername(user.username || '');
        setCurrentEmail(user.email || '');
        setCurrentRole(user.role || '');
        setCurrentAvatar(user.avatar || '');
        setTimezone(user.timezone || 'UTC');
        setDateFormat(user.date_format || 'MMDD, YYYY (Jun 15, 2023)');
      }
    } finally {
      setDataLoading(false);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      const token = getToken();
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch('http://localhost:5000/api/incident/notification-settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const settings = await response.json();
        setNotificationSettings({
          email_enabled: settings.email_enabled || false,
          email_address: settings.email_address || '',
          telegram_enabled: settings.telegram_enabled || false,
          telegram_chat_id: settings.telegram_chat_id || ''
        });
      }
    } catch (err) {
      console.error('Failed to load notification settings:', err);
    }
  };

  const saveNotificationSettings = async () => {
    setNotificationLoading(true);
    setNotificationError(null);
    setNotificationSuccess(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/api/incident/notification-settings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notificationSettings)
      });

      if (response.ok) {
        setNotificationSuccess('Notification settings saved successfully!');
        setTimeout(() => setNotificationSuccess(null), 3000);
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (err) {
      setNotificationError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setNotificationLoading(false);
    }
  };



  const validateForm = (updateData: any) => {
    const errors: {[key: string]: string} = {};

    if ('first_name' in updateData) {
      if (!updateData.first_name.trim()) {
        errors.firstName = 'First name cannot be empty';
      } else if (updateData.first_name.trim().length < 2) {
        errors.firstName = 'First name must be at least 2 characters';
      }
    }

    if ('last_name' in updateData) {
      if (!updateData.last_name.trim()) {
        errors.lastName = 'Last name cannot be empty';
      } else if (updateData.last_name.trim().length < 2) {
        errors.lastName = 'Last name must be at least 2 characters';
      }
    }

    if ('username' in updateData) {
      if (!updateData.username.trim()) {
        errors.username = 'Username cannot be empty';
      } else if (updateData.username.trim().length < 3) {
        errors.username = 'Username must be at least 3 characters';
      }
    }

    if ('email' in updateData) {
      if (!updateData.email.trim()) {
        errors.email = 'Email cannot be empty';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }

    return errors;
  };

  const handleSaveChanges = async () => {
    setProfileLoading(true);
    setProfileError(null);
    setProfileSuccess(null);

    // Only include fields that have been modified (non-empty)
    const updateData: any = {};

    if (firstName.trim()) {
      updateData.first_name = firstName.trim();
    }
    if (lastName.trim()) {
      updateData.last_name = lastName.trim();
    }
    if (username.trim()) {
      updateData.username = username.trim();
    }
    if (email.trim()) {
      updateData.email = email.trim();
    }

    // Always include timezone and date format
    updateData.timezone = timezone;
    updateData.date_format = dateFormat;

    // Check if there are any changes to save
    if (Object.keys(updateData).length === 2) { // Only timezone and date_format
      setProfileError('Please enter at least one field to update');
      setProfileLoading(false);
      return;
    }

    // Validate form
    const validationErrors = validateForm(updateData);
    if (Object.keys(validationErrors).length > 0) {
      setFieldErrors(validationErrors);
      setProfileError('Please fix the errors above');
      setProfileLoading(false);
      return;
    }

    // Clear field errors if validation passes
    setFieldErrors({});

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/auth/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (response.ok) {
        const result = await response.json();
        setProfileSuccess('Profile updated successfully!');
        setTimeout(() => setProfileSuccess(null), 3000);

        // Update local user data if needed
        if (result.user) {
          setFirstName(result.user.first_name || '');
          setLastName(result.user.last_name || '');
          setUsername(result.user.username || '');
          setEmail(result.user.email || '');
          setRole(result.user.role || '');
          setTimezone(result.user.timezone || 'UTC');
          setDateFormat(result.user.date_format || 'MMDD, YYYY (Jun 15, 2023)');
        }
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }
    } catch (err) {
      setProfileError(err instanceof Error ? err.message : 'Failed to update profile');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setAvatarLoading(true);
    setProfileError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch('http://localhost:5000/auth/avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        setCurrentAvatar(result.avatar_url);
        setAvatar(result.avatar_url);
        setProfileSuccess('Avatar updated successfully!');
        setTimeout(() => setProfileSuccess(null), 3000);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload avatar');
      }
    } catch (err) {
      setProfileError(err instanceof Error ? err.message : 'Failed to upload avatar');
    } finally {
      setAvatarLoading(false);
    }
  };

  const renderProfileTab = () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Profile Settings</h2>

        {/* Loading indicator */}
        {dataLoading && (
          <div className="bg-gray-900/60 border border-gray-700/50 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-400"></div>
              <p className="text-gray-300">Loading profile data...</p>
            </div>
          </div>
        )}

        {/* Success/Error Messages */}
        {profileSuccess && (
          <div className="bg-green-900/60 border border-green-700/50 rounded-xl p-4 mb-6">
            <p className="text-green-300">{profileSuccess}</p>
          </div>
        )}
        {profileError && (
          <div className="bg-red-900/60 border border-red-700/50 rounded-xl p-4 mb-6">
            <p className="text-red-300">{profileError}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Avatar Section */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <div className="text-center">
                <div className="relative inline-block">
                  {currentAvatar ? (
                    <img
                      src={`http://localhost:5000${currentAvatar}`}
                      alt="Avatar"
                      className="w-32 h-32 rounded-full object-cover shadow-2xl"
                    />
                  ) : (
                    <div className="w-32 h-32 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-2xl">
                      {currentFirstName && currentLastName ? `${currentFirstName[0]}${currentLastName[0]}` : 'JD'}
                    </div>
                  )}
                  <label className="absolute bottom-2 right-2 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full transition-colors shadow-lg cursor-pointer">
                    <Camera className="w-4 h-4" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                      disabled={avatarLoading}
                    />
                  </label>
                </div>
                <p className="mt-4 text-purple-400 text-sm">
                  {avatarLoading ? 'Uploading...' : 'Change Avatar'}
                </p>
              </div>
            </div>
          </div>

          {/* Profile Form */}
          <div className="lg:col-span-2">
            <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name</label>
                  <div className="mb-2">
                    <span className="text-sm text-gray-400">Current: </span>
                    <span className="text-white">{currentFirstName || 'Not set'}</span>
                  </div>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => {
                      setFirstName(e.target.value);
                      if (fieldErrors.firstName) {
                        setFieldErrors(prev => ({ ...prev, firstName: '' }));
                      }
                    }}
                    className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                      fieldErrors.firstName
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-600/50 focus:ring-purple-500'
                    }`}
                    placeholder="Type your new first name"
                  />
                  {fieldErrors.firstName && (
                    <p className="text-red-400 text-xs mt-1">{fieldErrors.firstName}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
                  <div className="mb-2">
                    <span className="text-sm text-gray-400">Current: </span>
                    <span className="text-white">{currentLastName || 'Not set'}</span>
                  </div>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => {
                      setLastName(e.target.value);
                      if (fieldErrors.lastName) {
                        setFieldErrors(prev => ({ ...prev, lastName: '' }));
                      }
                    }}
                    className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                      fieldErrors.lastName
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-600/50 focus:ring-purple-500'
                    }`}
                    placeholder="Type your new last name"
                  />
                  {fieldErrors.lastName && (
                    <p className="text-red-400 text-xs mt-1">{fieldErrors.lastName}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
                <div className="mb-2">
                  <span className="text-sm text-gray-400">Current: </span>
                  <span className="text-white">{currentUsername || 'Not set'}</span>
                </div>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => {
                    setUsername(e.target.value);
                    if (fieldErrors.username) {
                      setFieldErrors(prev => ({ ...prev, username: '' }));
                    }
                  }}
                  className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                    fieldErrors.username
                      ? 'border-red-500 focus:ring-red-500'
                      : 'border-gray-600/50 focus:ring-purple-500'
                  }`}
                  placeholder="Type your new username"
                />
                {fieldErrors.username && (
                  <p className="text-red-400 text-xs mt-1">{fieldErrors.username}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                <div className="mb-2">
                  <span className="text-sm text-gray-400">Current: </span>
                  <span className="text-white">{currentEmail || 'Not set'}</span>
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (fieldErrors.email) {
                      setFieldErrors(prev => ({ ...prev, email: '' }));
                    }
                  }}
                  className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                    fieldErrors.email
                      ? 'border-red-500 focus:ring-red-500'
                      : 'border-gray-600/50 focus:ring-purple-500'
                  }`}
                  placeholder="Type your new email address"
                />
                {fieldErrors.email && (
                  <p className="text-red-400 text-xs mt-1">{fieldErrors.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Role</label>
                <div className="w-full px-4 py-3 bg-gray-800/30 border border-gray-600/30 rounded-xl text-gray-300 flex items-center">
                  <span className="capitalize">{currentRole || 'User'}</span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Note: role can be changed by a Workspace administrator.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                  <select
                    value={timezone}
                    onChange={(e) => setTimezone(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="UTC">UTC</option>
                    <option value="EST">EST</option>
                    <option value="PST">PST</option>
                    <option value="CET">CET</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Date Format</label>
                  <select
                    value={dateFormat}
                    onChange={(e) => setDateFormat(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="MMDD, YYYY (Jun 15, 2023)">MMDD, YYYY (Jun 15, 2023)</option>
                    <option value="DD/MM/YYYY (15/06/2023)">DD/MM/YYYY (15/06/2023)</option>
                    <option value="YYYY-MM-DD (2023-06-15)">YYYY-MM-DD (2023-06-15)</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveChanges}
          disabled={profileLoading}
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:transform-none"
        >
          {profileLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Save className="w-4 h-4" />
          )}
          <span>{profileLoading ? 'Saving...' : 'Save Changes'}</span>
        </button>
      </div>
    </div>
  );

  const renderNotificationsTab = () => {
    if (!isAdmin) {
      return (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
            <Bell className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Admin Access Required</h3>
          <p className="text-gray-400">
            Notification settings are only available for administrators.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white mb-6">Notification Settings</h2>

        {/* Success/Error Messages */}
        {notificationSuccess && (
          <div className="bg-green-900/50 border border-green-500 rounded-xl p-4">
            <p className="text-green-200">{notificationSuccess}</p>
          </div>
        )}

        {notificationError && (
          <div className="bg-red-900/50 border border-red-500 rounded-xl p-4">
            <p className="text-red-200">{notificationError}</p>
          </div>
        )}

        {/* Email Notifications */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Mail size={20} className="text-blue-400" />
              <div>
                <h3 className="text-white font-medium">Email Notifications</h3>
                <p className="text-sm text-gray-400">Receive incident notifications via email</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notificationSettings.email_enabled}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  email_enabled: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {notificationSettings.email_enabled && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={notificationSettings.email_address}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  email_address: e.target.value
                }))}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          )}
        </div>

        {/* Telegram Notifications */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <MessageSquare size={20} className="text-cyan-400" />
              <div>
                <h3 className="text-white font-medium">Telegram Notifications</h3>
                <p className="text-sm text-gray-400">Receive incident notifications via Telegram</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notificationSettings.telegram_enabled}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  telegram_enabled: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {notificationSettings.telegram_enabled && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Telegram Chat ID
              </label>
              <input
                type="text"
                value={notificationSettings.telegram_chat_id}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  telegram_chat_id: e.target.value
                }))}
                placeholder="5694506830"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
              <p className="text-xs text-gray-500 mt-2">
                Get your Chat ID by messaging @userinfobot on Telegram
              </p>
            </div>
          )}
        </div>

        {/* WebSocket Status */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <div>
              <h3 className="text-white font-medium">Real-time Notifications</h3>
              <p className="text-sm text-gray-400">WebSocket notifications are always enabled for admins</p>
            </div>
          </div>
        </div>

        {/* Configuration Info */}
        <div className="bg-blue-900/30 border border-blue-500/50 rounded-2xl p-6">
          <div className="flex items-start space-x-3">
            <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Configuration Information</h3>
              <div className="text-sm text-gray-300 space-y-1">
                <p>• <strong>Telegram Bot:</strong> Configured with token ending in ...mdDA</p>
                <p>• <strong>Default Chat ID:</strong> 5694506830 (can be overridden per admin)</p>
                <p>• <strong>Supported Events:</strong> Ticket creation, updates, assignments, and conversions</p>
                <p>• <strong>Message Format:</strong> HTML with emojis and structured information</p>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={saveNotificationSettings}
            disabled={notificationLoading}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:transform-none"
          >
            {notificationLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>Save Notification Settings</span>
          </button>
        </div>
      </div>
    );
  };

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white mb-6">Security Settings</h2>

      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 space-y-6">
        <div>
          <h3 className="text-white font-medium mb-4">Two-Factor Authentication</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Add an extra layer of security to your account</p>
            </div>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
              Enable 2FA
            </button>
          </div>
        </div>

        <div>
          <h3 className="text-white font-medium mb-4">Change Password</h3>
          <div className="space-y-4">
            <input
              type="password"
              placeholder="Current password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <input
              type="password"
              placeholder="New password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <input
              type="password"
              placeholder="Confirm new password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
              Update Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOtherTabs = (tabId: string) => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
        <Shield className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-white mb-2">
        {tabs.find(tab => tab.id === tabId)?.label} Settings
      </h3>
      <p className="text-gray-400">
        Cette section sera implémentée prochainement.
      </p>
    </div>
  );

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-64 bg-gray-900/60 backdrop-blur-xl border-r border-gray-700/50 p-4">
        <h1 className="text-xl font-bold text-white mb-6">Settings</h1>
        <nav className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        {activeTab === 'profile' && renderProfileTab()}
        {activeTab === 'notifications' && renderNotificationsTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {!['profile', 'notifications', 'security'].includes(activeTab) && renderOtherTabs(activeTab)}
      </div>
    </div>
  );
}
